<?php
declare(strict_types=1);

namespace App\Core\Services\TestFile;

use App\Model\TestFile\FileSyncModel;
use App\Model\TestFile\ReorganizeTaskModel;
use App\Model\TestFile\FileProductModel;
use App\Model\TestFile\TestRecordModel;
use App\Model\TestFile\AgingTestResultModel;
use App\Model\TestFile\FactoryTestResultModel;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Logger\LoggerFactory;
use Psr\Log\LoggerInterface;
use Hyperf\Contract\ConfigInterface;

class ReorganizeService
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var ConfigInterface
     */
    protected $config;

    protected $sourceDir;
    protected $targetDir;

    public function __construct(LoggerFactory $loggerFactory, ConfigInterface $config)
    {
        $this->logger = $loggerFactory->get('reorganize');
        $this->config = $config;

        // 从配置文件读取目录设置
        $this->sourceDir = $this->config->get('test_file.test_file.source_dir', '/home/<USER>');
        $this->targetDir = $this->config->get('test_file.test_file.target_dir', '/home/<USER>');
    }

    /**
     * 执行目录重排任务
     */
    public function execute(array $options = []): array
    {
        $task = $this->createTask($options);

        try {
            $this->updateTaskStatus($task->id, 1); // 处理中

            $sourceDir = $options['source_dir'] ?? $this->sourceDir;
            $targetDir = $options['target_dir'] ?? $this->targetDir;

            // 处理日期范围参数
            $dateRange = $this->parseDateRange($options);

            $this->logger->info('开始执行重排任务', [
                'task_id' => $task->id,
                'source_dir' => $sourceDir,
                'target_dir' => $targetDir,
                'date_range' => $dateRange
            ]);

            // 创建目标目录
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
                $this->logger->info('创建目标目录', ['dir' => $targetDir]);
            }

            $files = $this->scanDirectoryWithDateFilter($sourceDir, $dateRange);
            $processedCount = 0;

            // 去重
            // 1. 计算 md5（生成 [(path + mtime) => md5] 的映射）
            $fileMd5Map = collect($files)->mapWithKeys(function ($path) {
                return [$path => md5($path.filemtime($path))];
            });
            // 2. 批量查数据库里已存在的 md5
            $existsMd5 = FileSyncModel::whereIn('file_md5', $fileMd5Map->values())->pluck('file_md5')->all();
            // 3. 差集：筛出数据库不存在的
            $newFiles = $fileMd5Map->reject(function ($md5) use ($existsMd5) {
                return in_array($md5, $existsMd5);
            });
            $newFiles = $newFiles->keys()->all();

            $newFilesCount = count($newFiles);
            $this->logger->info('扫描到新文件数量', ['count' => $newFilesCount]);

            foreach ($newFiles as $file) {
                if ($this->processFile($file, $sourceDir, $targetDir)) {
                    $processedCount++;
                }
            }

            $this->updateTaskStatus($task->id, 2, $processedCount, $newFilesCount); // 完成

            $this->logger->info('重排任务完成', [
                'task_id' => $task->id,
                'processed' => $processedCount,
                'total' => count($files)
            ]);
            return [
                'task_id' => $task->id,
                'processed' => $processedCount,
                'total' => count($files)
            ];

        } catch (\Exception $e) {
            $this->logger->info('重排任务失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);

            $this->updateTaskStatus($task->id, 3, 0, $newFilesCount,$e->getMessage()); // 失败
            throw $e;
        }
    }

    /**
     * 扫描目录获取文件列表
     */
    protected function scanDirectory(string $dir): array
    {
        if (!is_dir($dir)) {
            $this->logger->warning('目录不存在', ['dir' => $dir]);
            return [];
        }

        $files = [];
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * 根据日期范围扫描目录获取文件列表
     */
    protected function scanDirectoryWithDateFilter(string $dir, array $dateRange): array
    {
        if (!is_dir($dir)) {
            $this->logger->warning('目录不存在', ['dir' => $dir]);
            return [];
        }

        $files = [];
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        // 扫描顶级目录，查找符合日期范围的日期目录
        $dateDirectories = [];
        $dirIterator = new \DirectoryIterator($dir);
        
        foreach ($dirIterator as $item) {
            if ($item->isDot() || !$item->isDir()) {
                continue;
            }
            
            $dirName = $item->getFilename();
            // 检查目录名是否为8位数字的日期格式（YYYYMMDD）
            if (preg_match('/^\d{8}$/', $dirName)) {
                // 将YYYYMMDD格式转换为日期对象进行比较
                try {
                    $dirDate = Carbon::createFromFormat('Ymd', $dirName);
                    if ($dirDate->between($startDate, $endDate, true)) {
                        $dateDirectories[] = $item->getPathname();
                        $this->logger->info('找到符合日期范围的目录', [
                            'dir' => $dirName,
                            'date' => $dirDate->toDateString()
                        ]);
                    }
                } catch (\Exception $e) {
                    $this->logger->warning('日期目录格式解析失败', [
                        'dir' => $dirName,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        // 对符合条件的日期目录进行递归扫描
        foreach ($dateDirectories as $dateDir) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($dateDir, \RecursiveDirectoryIterator::SKIP_DOTS),
                \RecursiveIteratorIterator::SELF_FIRST
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $files[] = $file->getPathname();
                }
            }
        }

        $this->logger->info('扫描完成', [
            'date_directories' => count($dateDirectories),
            'total_files' => count($files),
            'date_range' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString()
            ]
        ]);

        return $files;
    }

    /**
     * 解析日期范围参数
     */
    protected function parseDateRange(array $options): array
    {
        $startDate = null;
        $endDate = null;

        // 处理前端传来的 date_range 参数
        if (isset($options['date_range']) && is_array($options['date_range']) && count($options['date_range']) === 2) {
            try {
                $startDate = Carbon::parse($options['date_range'][0]);
                $endDate = Carbon::parse($options['date_range'][1]);
                
                $this->logger->info('使用指定的日期范围', [
                    'start' => $startDate->toDateString(),
                    'end' => $endDate->toDateString()
                ]);
            } catch (\Exception $e) {
                $this->logger->warning('日期范围解析失败，使用默认值', [
                    'date_range' => $options['date_range'],
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 如果没有指定日期范围或解析失败，使用今天作为默认值
        if (!$startDate || !$endDate) {
            $today = Carbon::today();
            $startDate = $today->copy();
            $endDate = $today->copy();
            
            $this->logger->info('使用默认日期范围（今天）', [
                'date' => $today->toDateString()
            ]);
        }

        return [
            'start' => $startDate,
            'end' => $endDate
        ];
    }

    /**
     * 处理单个文件
     */
    protected function processFile(string $filePath, string $sourceBase, string $targetBase): bool
    {
        // 解析文件路径
        $pathInfo = $this->parseFilePath($filePath, $sourceBase);
        if (!$pathInfo) {
            $this->logger->warning('文件路径不符合规范，无法解析文件路径', ['file' => $filePath]);
            return false;
        }
        // 检查是否已处理
        $md5 = md5($filePath.filemtime($filePath));
        $existing = FileSyncModel::query()
            ->orWhere('file_md5', $md5)
            ->first();

        if ($existing) {
            $this->logger->warning('文件已存在或已删除', ['file' => $filePath]);
            return false;
        }

        // 构建目标路径
        $targetPath = $this->buildTargetPath($pathInfo, $targetBase);

        // 创建目标目录
        $targetDir = dirname($targetPath);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }

        // 复制文件
        if (!copy($filePath, $targetPath)) {
            $this->logger->error('文件复制失败', [
                'source' => $filePath,
                'target' => $targetPath
            ]);
            return false;
        }

        // 计算文件信息
        $fileInfo = $this->getFileInfo($filePath);

        // 写入前搜索dst_path相同的同名文件进行标记删除
        FileSyncModel::query()->where('dst_path', $targetPath)->update(['sync_status' => 3]);

        // 写入数据库
        $record = new FileSyncModel();
        $record->product = $pathInfo['product'];
        $record->sn = $pathInfo['sn'];
        $record->date_folder = $pathInfo['date_folder'];
        $record->test_datetime = $pathInfo['test_datetime'];
        $record->test_type = $pathInfo['test_type'];
        $record->filename = $pathInfo['filename'];
        $record->file_size = $fileInfo['size'];
        $record->file_mtime = Carbon::createFromTimestamp($fileInfo['mtime']);
        $record->src_path = $filePath;
        $record->dst_path = $targetPath;
        $record->file_md5 = $fileInfo['md5'];
        $record->sync_status = 2; // 已重排
        $record->save();

        // 解析测试文件内容（如果需要）
        $this->parseTestFile($targetPath, $pathInfo, $record->id);

        $this->logger->info('文件处理成功', [
            'file' => $pathInfo['filename'],
            'product' => $pathInfo['product'],
            'sn' => $pathInfo['sn']
        ]);

        return true;
    }

    /**
     * 解析文件路径
     */
    protected function parseFilePath(string $filePath, string $baseDir): ?array
    {
        $relativePath = str_replace($baseDir . '/', '', $filePath);
        $parts = explode('/', $relativePath);

        // 期望格式: 日期/产品/SN/测试时间/[测试类型/]文件名
        if (count($parts) < 5) {
            return null;
        }

        $dateFolder = $parts[0];
        $product = $parts[1];
        $sn = $parts[2];
        $testDatetime = $parts[3];

        // 判断是否有测试类型目录
        if (count($parts) == 6) {
            $testType = $parts[4];
            $filename = $parts[5];
        } else {
            $testType = null;
            $filename = $parts[4];
        }

        return [
            'date_folder' => $dateFolder,
            'product' => $product,
            'sn' => $sn,
            'test_datetime' => $testDatetime,
            'test_type' => $testType,
            'filename' => $filename,
            'relative_path' => $relativePath
        ];
    }

    /**
     * 构建目标路径
     */
    protected function buildTargetPath(array $pathInfo, string $targetBase): string
    {
        $path = $targetBase . '/' . $pathInfo['product'] . '/' .
            $pathInfo['sn'] . '/' . $pathInfo['test_datetime'];

        if ($pathInfo['test_type']) {
            $path .= '/' . $pathInfo['test_type'];
        }

        $path .= '/' . $pathInfo['filename'];

        return $path;
    }

    /**
     * 获取文件信息
     */
    protected function getFileInfo(string $filePath): array
    {
        return [
            'size' => filesize($filePath),
            'mtime' => filemtime($filePath),
            'md5' => md5($filePath.filemtime($filePath))
        ];
    }

    /**
     * 创建任务记录
     */
    protected function createTask(array $options): ReorganizeTaskModel
    {
        $task = new ReorganizeTaskModel();
        $task->task_type = $options['task_type'] ?? 'manual';
        $task->source_dir = $options['source_dir'] ?? $this->sourceDir;
        $task->target_dir = $options['target_dir'] ?? $this->targetDir;
        $task->status = 0;
        $task->save();

        return $task;
    }

    /**
     * 更新任务状态
     */
    protected function updateTaskStatus(int $taskId, int $status, int $processed = 0, int $total = 0, ?string $error = null): void
    {
        $task = ReorganizeTaskModel::find($taskId);
        if (!$task) {
            return;
        }

        $task->status = $status;

        if ($status === 1) {
            $task->started_at = Carbon::now();
        } elseif ($status === 2) {
            $task->completed_at = Carbon::now();
            $task->processed_files = $processed;
        } elseif ($status === 3) {
            $task->completed_at = Carbon::now();
            $task->error_message = $error;
        }
        $task->total_files = $total;

        $task->save();
    }

    /**
     * 获取任务列表
     */
    public function getTaskList(array $params): array
    {
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['page_size'] ?? 20);

        $query = ReorganizeTaskModel::query();

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        $total = $query->count();
        $list = $query->orderBy('id', 'desc')
            ->forPage($page, $pageSize)
            ->get();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取任务详情
     */
    public function getTaskById(int $id): ?array
    {
        $task = ReorganizeTaskModel::find($id);
        return $task ? $task->toArray() : null;
    }

    /**
     * 解析测试文件内容
     */
    protected function parseTestFile(string $filePath, array $pathInfo, int $fileSyncId = null): void
    {
        $filename = basename($filePath);

        // 确保产品存在
        $product = FileProductModel::where('product_name', $pathInfo['product'])->first();
        if (!$product) {
            $product = new FileProductModel();
            $product->product_name = $pathInfo['product'];
            $product->status = 1;
            $product->save();
        }

        // 获取或创建测试记录
        $testRecord = TestRecordModel::where('product_id', $product->id)
            ->where('sn', $pathInfo['sn'])
            ->where('test_datetime', $pathInfo['test_datetime'])
            ->first();

        if (!$testRecord) {
            $testRecord = new TestRecordModel();
            $testRecord->product_id = $product->id;
            $testRecord->sn = $pathInfo['sn'];
            $testRecord->test_date = Carbon::createFromFormat('Ymd', $pathInfo['date_folder'])->toDateString();
            $testRecord->test_datetime = $pathInfo['test_datetime'];
            $testRecord->file_count = 1;
            $testRecord->save();
        } else {
            $testRecord->file_count += 1;
            $testRecord->save();
        }

        // 根据文件名解析具体内容
        switch ($filename) {
            case 'agingTest_result.txt':
                // 解析老化测试结果（传递 fileSyncId）
                $this->parseAgingTestResult($filePath, $testRecord->id, $fileSyncId);
                break;

            case 'factoryTest_result.txt':
                // 解析厂测结果（传递 fileSyncId）
                $this->parseFactoryTestResult($filePath, $testRecord->id, $fileSyncId);
                break;

            case 'cpuid.txt':
                // 更新 CPU ID
                $cpuid = trim(file_get_contents($filePath));
                if ($cpuid) {
                    $testRecord->cpuid = $cpuid;
                    $testRecord->save();
                }
                break;
        }
    }

    /**
     * 解析老化测试结果（简化版 - 仅解析运行时间）
     */
    protected function parseAgingTestResult(string $filePath, int $testRecordId, int $fileSyncId = null): void
    {
        $content = file_get_contents($filePath);
        $data = [];
        
        // 解析运行时间
        if (preg_match('/运行时间:(\d+):(\d+):(\d+)/', $content, $matches)) {
            $data['runtime'] = $matches[0]; // 保存完整字符串 "运行时间:255:24:17"
            // 计算总秒数
            $hours = (int)$matches[1];
            $minutes = (int)$matches[2];
            $seconds = (int)$matches[3];
            $data['runtime_seconds'] = $hours * 3600 + $minutes * 60 + $seconds;
        }
        
        // 保存到关联表
        if ($fileSyncId && !empty($data)) {
            $fileSync = FileSyncModel::find($fileSyncId);
            if ($fileSync) {
                // 检查是否已存在解析记录
                $existing = AgingTestResultModel::where('file_sync_id', $fileSyncId)->first();
                if (!$existing) {
                    $agingResult = new AgingTestResultModel();
                    $agingResult->file_sync_id = $fileSyncId;
                    $agingResult->product = $fileSync->product;
                    $agingResult->sn = $fileSync->sn;
                    $agingResult->test_datetime = $fileSync->test_datetime;
                    $agingResult->fill($data);
                    $agingResult->parsed_at = Carbon::now();
                    $agingResult->save();
                }
                
                // 更新文件解析状态
                $fileSync->file_type = 'aging_test';
                $fileSync->is_parsed = 1;
                $fileSync->parsed_at = Carbon::now();
                $fileSync->save();
            }
        }
        
        // 更新测试记录
        TestRecordModel::where('id', $testRecordId)->update([
            'aging_test_result' => '完成'
        ]);
    }

    /**
     * 解析厂测结果（简化版 - 仅解析9个必要字段）
     */
    protected function parseFactoryTestResult(string $filePath, int $testRecordId, int $fileSyncId = null): void
    {
        $content = file_get_contents($filePath);
        $data = [];
        
        // 1. 设备名
        if (preg_match('/设备名:(.*?)\n/', $content, $matches)) {
            $data['device_name'] = trim($matches[1]);
        }
        
        // 2. CPUID 
        if (preg_match('/CPUID:(.*?)\n/', $content, $matches)) {
            $data['cpuid'] = trim($matches[1]);
        }
        
        // 3. 厂测版本
        if (preg_match('/厂测版本:(.*?)\n/', $content, $matches)) {
            $data['factory_test_version'] = trim($matches[1]);
        }
        
        // 4. 固件版本
        if (preg_match('/固件版本:(.*?)\n/', $content, $matches)) {
            $data['firmware_version'] = trim($matches[1]);
        }
        
        // 5. DDR
        if (preg_match('/DDR:(.*?)\n/', $content, $matches)) {
            $data['ddr'] = trim($matches[1]);
        }
        
        // 6. Flash
        if (preg_match('/Flash:(.*?)\n/', $content, $matches)) {
            $data['flash'] = trim($matches[1]);
        }
        
        // 7. 成功项目
        if (preg_match('/成功\s*项目:\d+\s*\[\s*([^\]]+)\s*\]/', $content, $matches)) {
            $data['success_projects'] = trim($matches[1]);
        }
        
        // 8. 失败项目
        if (preg_match('/失败\s*项目:\d+\s*\[\s*([^\]]+)\s*\]/', $content, $matches)) {
            $data['failed_projects'] = trim($matches[1]);
        }
        
        // 9. 厂测结果
        if (preg_match('/厂测结果:(成功|失败)/', $content, $matches)) {
            $data['factory_test_result'] = $matches[1];
        }
        
        // 保存到关联表
        if ($fileSyncId && !empty($data)) {
            $fileSync = FileSyncModel::find($fileSyncId);
            if ($fileSync) {
                // 检查是否已存在解析记录
                $existing = FactoryTestResultModel::where('file_sync_id', $fileSyncId)->first();
                if (!$existing) {
                    $factoryResult = new FactoryTestResultModel();
                    $factoryResult->file_sync_id = $fileSyncId;
                    $factoryResult->product = $fileSync->product;
                    $factoryResult->sn = $fileSync->sn;
                    $factoryResult->test_datetime = $fileSync->test_datetime;
                    $factoryResult->fill($data);
                    $factoryResult->parsed_at = Carbon::now();
                    $factoryResult->save();
                }
                
                // 更新文件解析状态
                $fileSync->file_type = 'factory_test';
                $fileSync->is_parsed = 1;
                $fileSync->parsed_at = Carbon::now();
                $fileSync->save();
            }
        }
        
        // 更新测试记录
        $result = $data['factory_test_result'] ?? '未知';
        TestRecordModel::where('id', $testRecordId)->update([
            'factory_test_result' => $result
        ]);
    }

}
